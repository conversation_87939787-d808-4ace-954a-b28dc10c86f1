//@version=6
strategy("UCAS Optimized - High Frequency", 
         shorttitle="UCAS-HF", 
         overlay=true, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=15,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         calc_on_every_tick=true)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 OPTIMIZED STRATEGY FOR HIGHER TRADE FREQUENCY
// ═══════════════════════════════════════════════════════════════════════════════════════
// UCAS Optimized - High Frequency Version
// 
// 🎯 OPTIMIZATIONS FOR 10% MONTHLY TARGET:
// - Reduced confluence requirements (3/6 instead of 4/6)
// - Lower ADX threshold for more opportunities
// - Wider RSI ranges for crypto volatility
// - Faster EMA periods for quicker signals
// - Reduced volume threshold for more entries
// - More aggressive take profit ratios
// ═══════════════════════════════════════════════════════════════════════════════════════

// 🔧 OPTIMIZED INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════
group_trend = "🔄 Trend Analysis (Optimized)"
ema_fast_length = input.int(8, "Fast EMA Length", minval=5, maxval=20, group=group_trend)
ema_slow_length = input.int(21, "Slow EMA Length", minval=15, maxval=50, group=group_trend)
adx_length = input.int(14, "ADX Length", minval=10, maxval=25, group=group_trend)
adx_threshold = input.float(20.0, "ADX Threshold", minval=15.0, maxval=30.0, group=group_trend)

group_momentum = "⚡ Momentum Indicators (Optimized)"
rsi_length = input.int(12, "RSI Length", minval=8, maxval=20, group=group_momentum)
rsi_oversold = input.float(25.0, "RSI Oversold", minval=15.0, maxval=35.0, group=group_momentum)
rsi_overbought = input.float(75.0, "RSI Overbought", minval=65.0, maxval=85.0, group=group_momentum)
macd_fast = input.int(10, "MACD Fast", minval=6, maxval=15, group=group_momentum)
macd_slow = input.int(22, "MACD Slow", minval=18, maxval=30, group=group_momentum)
macd_signal = input.int(7, "MACD Signal", minval=5, maxval=12, group=group_momentum)

group_volume = "📊 Volume Analysis (Optimized)"
volume_ma_length = input.int(15, "Volume MA Length", minval=10, maxval=30, group=group_volume)
volume_threshold = input.float(1.1, "Volume Threshold Multiplier", minval=1.0, maxval=1.5, group=group_volume)

group_risk = "🛡️ Risk Management (Optimized)"
atr_length = input.int(12, "ATR Length", minval=8, maxval=20, group=group_risk)
stop_loss_atr = input.float(2.0, "Stop Loss (ATR Multiplier)", minval=1.5, maxval=3.0, group=group_risk)
take_profit_atr = input.float(4.0, "Take Profit (ATR Multiplier)", minval=2.5, maxval=6.0, group=group_risk)
risk_per_trade = input.float(3.0, "Risk Per Trade (%)", minval=1.0, maxval=5.0, group=group_risk)

group_signals = "🎯 Signal Settings (Optimized)"
confluence_required = input.int(3, "Confluence Required (out of 6)", minval=2, maxval=5, group=group_signals)
show_signals = input.bool(true, "Show Buy/Sell Signals", group=group_signals)
show_levels = input.bool(true, "Show Support/Resistance", group=group_signals)

// 📈 TECHNICAL INDICATORS CALCULATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Exponential Moving Averages (Faster)
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)

// Average Directional Index (Lower threshold)
[diplus, diminus, adx] = ta.dmi(adx_length, adx_length)

// Relative Strength Index (Faster)
rsi = ta.rsi(close, rsi_length)

// MACD (Faster settings)
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// Average True Range (Faster)
atr = ta.atr(atr_length)

// Volume Analysis (Lower threshold)
volume_ma = ta.sma(volume, volume_ma_length)
volume_above_avg = volume > (volume_ma * volume_threshold)

// Support and Resistance Levels (More responsive)
pivot_high = ta.pivothigh(high, 3, 3)
pivot_low = ta.pivotlow(low, 3, 3)

var float resistance_level = na
var float support_level = na

if not na(pivot_high)
    resistance_level := pivot_high
if not na(pivot_low)
    support_level := pivot_low

// 🧠 MARKET REGIME DETECTION (More Permissive)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Determine if market is trending or ranging (Lower threshold)
trending_market = adx > adx_threshold
ranging_market = not trending_market

// Trend direction (More sensitive)
uptrend = ema_fast > ema_slow and close > ema_fast
downtrend = ema_fast < ema_slow and close < ema_fast
sideways = not uptrend and not downtrend

// Market volatility adaptation
high_volatility = atr > ta.sma(atr, 15) * 1.3
normal_volatility = not high_volatility

// 🎯 OPTIMIZED SIGNAL GENERATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════

// State management for signal alternation
var string last_signal = "NONE"
var float entry_price = na
var float stop_loss_price = na
var float take_profit_price = na

// BUY Signal Conditions (More Permissive)
buy_condition_1 = uptrend  // Trend alignment
buy_condition_2 = rsi > rsi_oversold and rsi < 65  // RSI not overbought, wider range
buy_condition_3 = macd_line > signal_line or histogram > histogram[1]  // MACD bullish (OR condition)
buy_condition_4 = volume_above_avg or close > close[1]  // Volume OR price momentum
buy_condition_5 = trending_market or ranging_market  // Accept both market types
buy_condition_6 = close > support_level or na(support_level) or close > close[2]  // Multiple support conditions

// Count true conditions for BUY signal
buy_score = 0
buy_score += buy_condition_1 ? 1 : 0
buy_score += buy_condition_2 ? 1 : 0
buy_score += buy_condition_3 ? 1 : 0
buy_score += buy_condition_4 ? 1 : 0
buy_score += buy_condition_5 ? 1 : 0
buy_score += buy_condition_6 ? 1 : 0

// SELL Signal Conditions (More Permissive)
sell_condition_1 = downtrend or rsi > rsi_overbought  // Trend reversal or overbought
sell_condition_2 = macd_line < signal_line or histogram < histogram[1]  // MACD bearish
sell_condition_3 = close < resistance_level or na(resistance_level) or rsi > 80  // Multiple resistance conditions
sell_condition_4 = not na(entry_price) and close > entry_price * 1.03  // Lower profit protection (3%)
sell_condition_5 = rsi > 70 or close < ema_fast  // Multiple exit conditions
sell_condition_6 = volume_above_avg or close < close[1]  // Volume OR price weakness

// Count true conditions for SELL signal
sell_score = 0
sell_score += sell_condition_1 ? 1 : 0
sell_score += sell_condition_2 ? 1 : 0
sell_score += sell_condition_3 ? 1 : 0
sell_score += sell_condition_4 ? 1 : 0
sell_score += sell_condition_5 ? 1 : 0
sell_score += sell_condition_6 ? 1 : 0

// Signal Generation with Reduced Confluence Requirement
generate_buy_signal = buy_score >= confluence_required and last_signal != "BUY" and strategy.position_size == 0
generate_sell_signal = sell_score >= confluence_required and last_signal == "BUY" and strategy.position_size > 0

// 💰 OPTIMIZED RISK MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate position size based on risk percentage
calculate_position_size(entry_price, stop_price, risk_percent) =>
    risk_amount = strategy.equity * (risk_percent / 100)
    price_diff = math.abs(entry_price - stop_price)
    position_size = risk_amount / price_diff
    position_size

// More aggressive ATR multipliers for higher frequency
dynamic_stop_multiplier = high_volatility ? stop_loss_atr * 1.1 : stop_loss_atr
dynamic_tp_multiplier = high_volatility ? take_profit_atr * 1.1 : take_profit_atr

// 📋 STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

if generate_buy_signal
    // Calculate levels
    entry_price := close
    stop_loss_price := entry_price - (atr * dynamic_stop_multiplier)
    take_profit_price := entry_price + (atr * dynamic_tp_multiplier)
    
    // Calculate position size
    position_size = calculate_position_size(entry_price, stop_loss_price, risk_per_trade)
    
    // Execute trade with alert messages
    strategy.entry("BUY", strategy.long, qty=position_size, 
                   alert_message="UCAS-HF BUY: Entry " + str.tostring(close) + " | SL: " + str.tostring(stop_loss_price) + " | TP: " + str.tostring(take_profit_price) + " | Score: " + str.tostring(buy_score) + "/6")
    strategy.exit("BUY_EXIT", "BUY", stop=stop_loss_price, limit=take_profit_price,
                  alert_message="UCAS-HF Exit: SL/TP triggered")
    
    // Update state
    last_signal := "BUY"

if generate_sell_signal
    // Close position with alert message
    strategy.close("BUY", comment="SELL Signal", 
                   alert_message="UCAS-HF SELL: Exit " + str.tostring(close) + " | Score: " + str.tostring(sell_score) + "/6")
    
    // Update state
    last_signal := "SELL"
    entry_price := na
    stop_loss_price := na
    take_profit_price := na

// 🎨 VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ema_fast, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow, "Slow EMA", color=color.red, linewidth=2)

// Plot Support/Resistance
plot(show_levels and not na(support_level) ? support_level : na, "Support", color=color.green, style=plot.style_line, linewidth=1)
plot(show_levels and not na(resistance_level) ? resistance_level : na, "Resistance", color=color.red, style=plot.style_line, linewidth=1)

// Plot Signals
plotshape(show_signals and generate_buy_signal, "BUY Signal", shape.triangleup, location.belowbar, color.lime, size=size.normal)
plotshape(show_signals and generate_sell_signal, "SELL Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Plot Stop Loss and Take Profit levels
plot(strategy.position_size > 0 and not na(stop_loss_price) ? stop_loss_price : na, "Stop Loss", color=color.red, style=plot.style_cross)
plot(strategy.position_size > 0 and not na(take_profit_price) ? take_profit_price : na, "Take Profit", color=color.green, style=plot.style_cross)

// 📊 OPTIMIZED TABLE DISPLAY
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Strategy", text_color=color.black, bgcolor=color.orange)
    table.cell(info_table, 1, 0, "UCAS-HF v6", text_color=color.black)
    table.cell(info_table, 0, 1, "Last Signal", text_color=color.black)
    table.cell(info_table, 1, 1, last_signal, text_color=last_signal == "BUY" ? color.green : color.red)
    table.cell(info_table, 0, 2, "Market Regime", text_color=color.black)
    table.cell(info_table, 1, 2, trending_market ? "TRENDING" : "RANGING", text_color=color.black)
    table.cell(info_table, 0, 3, "Trend", text_color=color.black)
    table.cell(info_table, 1, 3, uptrend ? "UP" : downtrend ? "DOWN" : "SIDEWAYS", 
               text_color=uptrend ? color.green : downtrend ? color.red : color.orange)
    table.cell(info_table, 0, 4, "RSI", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)), text_color=color.black)
    table.cell(info_table, 0, 5, "ADX", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(math.round(adx, 1)), text_color=color.black)
    table.cell(info_table, 0, 6, "Buy Score", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(buy_score) + "/6", text_color=buy_score >= confluence_required ? color.green : color.black)
    table.cell(info_table, 0, 7, "Sell Score", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(sell_score) + "/6", text_color=sell_score >= confluence_required ? color.red : color.black)
